import { useEffect } from 'react'
import { useUserStatsStore } from '@/stores/userStatsStore'

/**
 * Hook to access user stats with automatic fetching
 */
export function useUserStats() {
  const stats = useUserStatsStore((state) => state.stats)
  const isLoading = useUserStatsStore((state) => state.isLoading)
  const error = useUserStatsStore((state) => state.error)
  const fetchStats = useUserStatsStore((state) => state.fetchStats)
  const isStale = useUserStatsStore((state) => state.isStale)
  const hasData = useUserStatsStore((state) => state.hasData)

  // Fetch stats on mount only if data is stale or missing
  useEffect(() => {
    // Only fetch if:
    // 1. We don't have data OR the data is stale
    // 2. Not currently loading
    // 3. No recent error
    const shouldFetch = (!hasData() || isStale()) && !isLoading && !error

    if (process.env.NODE_ENV === 'development') {
      console.log('[useUserStats] Effect triggered', {
        hasData: hasData(),
        isStale: isStale(),
        isLoading,
        error,
        shouldFetch,
        stats,
      })
    }

    if (shouldFetch) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[useUserStats] Fetching stats...')
      }
      fetchStats()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasData, isStale, isLoading, fetchStats]) // Intentionally omit error to prevent unwanted re-fetches

  const returnStats = stats || { weekStreak: 0, workoutsCompleted: 0, lbsLifted: 0 }

  if (process.env.NODE_ENV === 'development') {
    console.log('[useUserStats] Returning stats', {
      returnStats,
      isLoading,
      error,
    })
  }

  return {
    stats: returnStats,
    isLoading,
    error,
    refetch: () => fetchStats(true), // Force refresh when refetch is called
  }
}

/**
 * Hook to prefetch user stats (used during login)
 */
export function usePrefetchUserStats() {
  const fetchStats = useUserStatsStore((state) => state.fetchStats)

  return {
    prefetch: () => {
      // Fire and forget - don't await
      fetchStats()
    },
  }
}
